-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS http;

-- Create or replace the SMS maturity alert function
CREATE OR REPLACE FUNCTION public.send_maturity_alerts_sms()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    alert_days integer;
    sms_enabled boolean;
    target_date date;
    investment_record record;
    sms_count integer := 0;
    result_json json;
    twilio_account_sid text;
    twilio_auth_token text;
    twilio_phone_number text;
    http_response http_response;
    phone_numbers text[];
    phone_number text;
    message_body text;
    client_name text;
BEGIN
    -- Get notification settings
    SELECT 
        ns.alert_days_before, 
        ns.sms_enabled,
        ns.twilio_account_sid,
        ns.twilio_auth_token,
        ns.twilio_phone_number
    INTO 
        alert_days, 
        sms_enabled,
        twilio_account_sid,
        twilio_auth_token,
        twilio_phone_number
    FROM notification_settings ns
    LIMIT 1;
   
    -- Check if SMS is enabled
    IF NOT sms_enabled THEN
        RETURN json_build_object(
            'success', false,
            'message', 'SMS notifications are disabled'
        );
    END IF;
    
    -- Check if Twilio credentials are available
    IF twilio_account_sid IS NULL OR twilio_auth_token IS NULL OR twilio_phone_number IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Twilio configuration missing in database settings'
        );
    END IF;
   
    -- Calculate target maturity date
    target_date := CURRENT_DATE + INTERVAL '1 day' * COALESCE(alert_days, 7);
   
    -- Loop through investments maturing on target date
    FOR investment_record IN
        SELECT
            i.id as investment_id,
            i.scheme_name,
            i.scheme_code,
            i.amount,
            i.maturity_amount,
            i.maturity_date,
            i.interest_rate,
            -- Primary client details
            c1.id as primary_client_id,
            c1.first_name as primary_first_name,
            c1.last_name as primary_last_name,
            c1.mobile_number as primary_mobile,
            c1.contact_person2 as primary_contact_person2,
            c1.country_code as primary_country_code,
            -- Secondary client details
            c2.id as secondary_client_id,
            c2.first_name as secondary_first_name,
            c2.last_name as secondary_last_name,
            c2.mobile_number as secondary_mobile,
            c2.contact_person2 as secondary_contact_person2,
            c2.country_code as secondary_country_code
        FROM investments i
        JOIN clients c1 ON i.client_id = c1.id
        LEFT JOIN clients c2 ON i.second_applicant_id = c2.id
        WHERE i.status = 'active'
        AND i.maturity_date = target_date
        AND c1.is_deleted = false
        AND (c2.id IS NULL OR c2.is_deleted = false)
        -- Check if we haven't already sent an SMS alert today
        AND NOT EXISTS (
            SELECT 1 FROM alerts a 
            WHERE a.investment_id = i.id 
            AND a.alert_type = 'maturity_reminder'
            AND a.channel = 'sms'
            AND a.alert_date = CURRENT_DATE
        )
    LOOP
        -- Collect all phone numbers for this investment
        phone_numbers := ARRAY[]::text[];
        
        -- Add primary client mobile (only +91 numbers)
        IF investment_record.primary_country_code = '+91' AND investment_record.primary_mobile IS NOT NULL THEN
            phone_numbers := array_append(phone_numbers, 
                CASE 
                    WHEN investment_record.primary_mobile ~ '^\+91' THEN investment_record.primary_mobile
                    WHEN investment_record.primary_mobile ~ '^91' THEN '+' || investment_record.primary_mobile
                    WHEN length(investment_record.primary_mobile) = 10 THEN '+91' || investment_record.primary_mobile
                    ELSE investment_record.primary_mobile
                END
            );
        END IF;
        
        -- Add primary client contact_person2 (only +91 numbers)
        IF investment_record.primary_country_code = '+91' AND investment_record.primary_contact_person2 IS NOT NULL THEN
            phone_numbers := array_append(phone_numbers, 
                CASE 
                    WHEN investment_record.primary_contact_person2 ~ '^\+91' THEN investment_record.primary_contact_person2
                    WHEN investment_record.primary_contact_person2 ~ '^91' THEN '+' || investment_record.primary_contact_person2
                    WHEN length(investment_record.primary_contact_person2) = 10 THEN '+91' || investment_record.primary_contact_person2
                    ELSE investment_record.primary_contact_person2
                END
            );
        END IF;
        
        -- Add secondary client mobile (only +91 numbers)
        IF investment_record.secondary_client_id IS NOT NULL 
           AND investment_record.secondary_country_code = '+91' 
           AND investment_record.secondary_mobile IS NOT NULL THEN
            phone_numbers := array_append(phone_numbers, 
                CASE 
                    WHEN investment_record.secondary_mobile ~ '^\+91' THEN investment_record.secondary_mobile
                    WHEN investment_record.secondary_mobile ~ '^91' THEN '+' || investment_record.secondary_mobile
                    WHEN length(investment_record.secondary_mobile) = 10 THEN '+91' || investment_record.secondary_mobile
                    ELSE investment_record.secondary_mobile
                END
            );
        END IF;
        
        -- Add secondary client contact_person2 (only +91 numbers)
        IF investment_record.secondary_client_id IS NOT NULL 
           AND investment_record.secondary_country_code = '+91' 
           AND investment_record.secondary_contact_person2 IS NOT NULL THEN
            phone_numbers := array_append(phone_numbers, 
                CASE 
                    WHEN investment_record.secondary_contact_person2 ~ '^\+91' THEN investment_record.secondary_contact_person2
                    WHEN investment_record.secondary_contact_person2 ~ '^91' THEN '+' || investment_record.secondary_contact_person2
                    WHEN length(investment_record.secondary_contact_person2) = 10 THEN '+91' || investment_record.secondary_contact_person2
                    ELSE investment_record.secondary_contact_person2
                END
            );
        END IF;
        
        -- Create personalized message
        client_name := investment_record.primary_first_name || COALESCE(' ' || investment_record.primary_last_name, '');
        IF investment_record.secondary_client_id IS NOT NULL THEN
            client_name := client_name || ' & ' || investment_record.secondary_first_name || COALESCE(' ' || investment_record.secondary_last_name, '');
        END IF;
        
        message_body := 'Dear ' || client_name || ', your investment in ' || investment_record.scheme_name || 
                       ' (₹' || investment_record.amount || ') will mature on ' || 
                       to_char(investment_record.maturity_date, 'DD-MM-YYYY') || 
                       '. Maturity amount: ₹' || investment_record.maturity_amount || 
                       '. Please contact us for further action. - Investment Team';
        
        -- Send SMS to all collected phone numbers
        FOREACH phone_number IN ARRAY phone_numbers
        LOOP
            -- Only send to +91 numbers
            IF phone_number ~ '^\+91\d{10}$' THEN
                -- Send SMS via Twilio API
                SELECT * FROM http((
                    'POST',
                    'https://api.twilio.com/2010-04-01/Accounts/' || twilio_account_sid || '/Messages.json',
                    ARRAY[
                        http_header('Authorization', 'Basic ' || encode(twilio_account_sid || ':' || twilio_auth_token, 'base64')),
                        http_header('Content-Type', 'application/x-www-form-urlencoded')
                    ],
                    'application/x-www-form-urlencoded',
                    'To=' || phone_number || '&From=' || twilio_phone_number || '&Body=' || message_body
                )) INTO http_response;
                
                -- Log the alert
                IF http_response.status = 201 THEN
                    INSERT INTO alerts (
                        investment_id, 
                        alert_type, 
                        alert_date, 
                        message, 
                        status, 
                        channel
                    ) VALUES (
                        investment_record.investment_id,
                        'maturity_reminder',
                        CURRENT_DATE,
                        'SMS sent to ' || phone_number || ': ' || message_body,
                        'sent',
                        'sms'
                    );
                    sms_count := sms_count + 1;
                ELSE
                    INSERT INTO alerts (
                        investment_id, 
                        alert_type, 
                        alert_date, 
                        message, 
                        status, 
                        channel
                    ) VALUES (
                        investment_record.investment_id,
                        'maturity_reminder',
                        CURRENT_DATE,
                        'SMS failed to ' || phone_number || ': ' || message_body || ' (Status: ' || http_response.status || ')',
                        'failed',
                        'sms'
                    );
                END IF;
            END IF;
        END LOOP;
    END LOOP;
   
    -- Return result
    result_json := json_build_object(
        'success', true,
        'message', 'Processed ' || sms_count || ' SMS maturity alerts',
        'target_date', target_date,
        'alert_days_before', alert_days,
        'sms_sent', sms_count
    );
   
    RETURN result_json;
END;
$$;

-- Schedule the cron job to run daily at 9 AM
SELECT cron.schedule(
  'send-maturity-alerts-sms-daily',
  '0 9 * * *', -- Daily at 9 AM
  $$
  SELECT public.send_maturity_alerts_sms();
  $$
);

-- Add a function to manually trigger the SMS alert for testing
CREATE OR REPLACE FUNCTION public.trigger_maturity_sms_alerts()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN public.send_maturity_alerts_sms();
END;
$$;

-- Create function to send SMS for specific investment
CREATE OR REPLACE FUNCTION public.send_investment_sms_alert(investment_uuid UUID, custom_message TEXT DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    sms_enabled boolean;
    investment_record record;
    result_json json;
    twilio_account_sid text;
    twilio_auth_token text;
    twilio_phone_number text;
    http_response http_response;
    phone_numbers text[];
    phone_number text;
    message_body text;
    client_name text;
    sms_count integer := 0;
BEGIN
    -- Get notification settings
    SELECT
        ns.sms_enabled,
        ns.twilio_account_sid,
        ns.twilio_auth_token,
        ns.twilio_phone_number
    INTO
        sms_enabled,
        twilio_account_sid,
        twilio_auth_token,
        twilio_phone_number
    FROM notification_settings ns
    LIMIT 1;

    -- Check if SMS is enabled
    IF NOT sms_enabled THEN
        RETURN json_build_object(
            'success', false,
            'message', 'SMS notifications are disabled'
        );
    END IF;

    -- Check if Twilio credentials are available
    IF twilio_account_sid IS NULL OR twilio_auth_token IS NULL OR twilio_phone_number IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Twilio configuration missing in database settings'
        );
    END IF;

    -- Get investment details
    SELECT
        i.id as investment_id,
        i.scheme_name,
        i.scheme_code,
        i.amount,
        i.maturity_amount,
        i.maturity_date,
        i.interest_rate,
        -- Primary client details
        c1.id as primary_client_id,
        c1.first_name as primary_first_name,
        c1.last_name as primary_last_name,
        c1.mobile_number as primary_mobile,
        c1.contact_person2 as primary_contact_person2,
        c1.country_code as primary_country_code,
        -- Secondary client details
        c2.id as secondary_client_id,
        c2.first_name as secondary_first_name,
        c2.last_name as secondary_last_name,
        c2.mobile_number as secondary_mobile,
        c2.contact_person2 as secondary_contact_person2,
        c2.country_code as secondary_country_code
    INTO investment_record
    FROM investments i
    JOIN clients c1 ON i.client_id = c1.id
    LEFT JOIN clients c2 ON i.second_applicant_id = c2.id
    WHERE i.id = investment_uuid
    AND c1.is_deleted = false
    AND (c2.id IS NULL OR c2.is_deleted = false);

    -- Check if investment exists
    IF investment_record.investment_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Investment not found'
        );
    END IF;

    -- Collect all phone numbers for this investment
    phone_numbers := ARRAY[]::text[];

    -- Add primary client mobile (only +91 numbers)
    IF investment_record.primary_country_code = '+91' AND investment_record.primary_mobile IS NOT NULL THEN
        phone_numbers := array_append(phone_numbers,
            CASE
                WHEN investment_record.primary_mobile ~ '^\+91' THEN investment_record.primary_mobile
                WHEN investment_record.primary_mobile ~ '^91' THEN '+' || investment_record.primary_mobile
                WHEN length(investment_record.primary_mobile) = 10 THEN '+91' || investment_record.primary_mobile
                ELSE investment_record.primary_mobile
            END
        );
    END IF;

    -- Add primary client contact_person2 (only +91 numbers)
    IF investment_record.primary_country_code = '+91' AND investment_record.primary_contact_person2 IS NOT NULL THEN
        phone_numbers := array_append(phone_numbers,
            CASE
                WHEN investment_record.primary_contact_person2 ~ '^\+91' THEN investment_record.primary_contact_person2
                WHEN investment_record.primary_contact_person2 ~ '^91' THEN '+' || investment_record.primary_contact_person2
                WHEN length(investment_record.primary_contact_person2) = 10 THEN '+91' || investment_record.primary_contact_person2
                ELSE investment_record.primary_contact_person2
            END
        );
    END IF;

    -- Add secondary client mobile (only +91 numbers)
    IF investment_record.secondary_client_id IS NOT NULL
       AND investment_record.secondary_country_code = '+91'
       AND investment_record.secondary_mobile IS NOT NULL THEN
        phone_numbers := array_append(phone_numbers,
            CASE
                WHEN investment_record.secondary_mobile ~ '^\+91' THEN investment_record.secondary_mobile
                WHEN investment_record.secondary_mobile ~ '^91' THEN '+' || investment_record.secondary_mobile
                WHEN length(investment_record.secondary_mobile) = 10 THEN '+91' || investment_record.secondary_mobile
                ELSE investment_record.secondary_mobile
            END
        );
    END IF;

    -- Add secondary client contact_person2 (only +91 numbers)
    IF investment_record.secondary_client_id IS NOT NULL
       AND investment_record.secondary_country_code = '+91'
       AND investment_record.secondary_contact_person2 IS NOT NULL THEN
        phone_numbers := array_append(phone_numbers,
            CASE
                WHEN investment_record.secondary_contact_person2 ~ '^\+91' THEN investment_record.secondary_contact_person2
                WHEN investment_record.secondary_contact_person2 ~ '^91' THEN '+' || investment_record.secondary_contact_person2
                WHEN length(investment_record.secondary_contact_person2) = 10 THEN '+91' || investment_record.secondary_contact_person2
                ELSE investment_record.secondary_contact_person2
            END
        );
    END IF;

    -- Create personalized message
    client_name := investment_record.primary_first_name || COALESCE(' ' || investment_record.primary_last_name, '');
    IF investment_record.secondary_client_id IS NOT NULL THEN
        client_name := client_name || ' & ' || investment_record.secondary_first_name || COALESCE(' ' || investment_record.secondary_last_name, '');
    END IF;

    -- Use custom message or default maturity message
    IF custom_message IS NOT NULL THEN
        message_body := replace(custom_message, '{CLIENT_NAME}', client_name);
        message_body := replace(message_body, '{SCHEME_NAME}', investment_record.scheme_name);
        message_body := replace(message_body, '{AMOUNT}', '₹' || investment_record.amount::text);
        message_body := replace(message_body, '{MATURITY_AMOUNT}', '₹' || investment_record.maturity_amount::text);
        message_body := replace(message_body, '{MATURITY_DATE}', to_char(investment_record.maturity_date, 'DD-MM-YYYY'));
    ELSE
        message_body := 'Dear ' || client_name || ', your investment in ' || investment_record.scheme_name ||
                       ' (₹' || investment_record.amount || ') will mature on ' ||
                       to_char(investment_record.maturity_date, 'DD-MM-YYYY') ||
                       '. Maturity amount: ₹' || investment_record.maturity_amount ||
                       '. Please contact us for further action. - Investment Team';
    END IF;

    -- Send SMS to all collected phone numbers
    FOREACH phone_number IN ARRAY phone_numbers
    LOOP
        -- Only send to +91 numbers
        IF phone_number ~ '^\+91\d{10}$' THEN
            -- Send SMS via Twilio API
            SELECT * FROM http((
                'POST',
                'https://api.twilio.com/2010-04-01/Accounts/' || twilio_account_sid || '/Messages.json',
                ARRAY[
                    http_header('Authorization', 'Basic ' || encode(twilio_account_sid || ':' || twilio_auth_token, 'base64')),
                    http_header('Content-Type', 'application/x-www-form-urlencoded')
                ],
                'application/x-www-form-urlencoded',
                'To=' || phone_number || '&From=' || twilio_phone_number || '&Body=' || message_body
            )) INTO http_response;

            -- Log the alert
            IF http_response.status = 201 THEN
                INSERT INTO alerts (
                    investment_id,
                    alert_type,
                    alert_date,
                    message,
                    status,
                    channel
                ) VALUES (
                    investment_record.investment_id,
                    'manual_sms',
                    CURRENT_DATE,
                    'SMS sent to ' || phone_number || ': ' || message_body,
                    'sent',
                    'sms'
                );
                sms_count := sms_count + 1;
            ELSE
                INSERT INTO alerts (
                    investment_id,
                    alert_type,
                    alert_date,
                    message,
                    status,
                    channel
                ) VALUES (
                    investment_record.investment_id,
                    'manual_sms',
                    CURRENT_DATE,
                    'SMS failed to ' || phone_number || ': ' || message_body || ' (Status: ' || http_response.status || ')',
                    'failed',
                    'sms'
                );
            END IF;
        END IF;
    END LOOP;

    -- Return result
    result_json := json_build_object(
        'success', true,
        'message', 'Sent ' || sms_count || ' SMS alerts for investment',
        'investment_id', investment_record.investment_id,
        'sms_sent', sms_count,
        'phone_numbers_processed', array_length(phone_numbers, 1)
    );

    RETURN result_json;
END;
$$;

-- Create function to get SMS alert statistics
CREATE OR REPLACE FUNCTION public.get_sms_alert_stats(days_back INTEGER DEFAULT 30)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result_json json;
    total_sent integer;
    total_failed integer;
    total_today integer;
BEGIN
    -- Get SMS statistics
    SELECT
        COUNT(*) FILTER (WHERE status = 'sent') as sent_count,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_count,
        COUNT(*) FILTER (WHERE status = 'sent' AND alert_date = CURRENT_DATE) as today_count
    INTO total_sent, total_failed, total_today
    FROM alerts
    WHERE channel = 'sms'
    AND alert_date >= CURRENT_DATE - INTERVAL '1 day' * days_back;

    result_json := json_build_object(
        'success', true,
        'period_days', days_back,
        'total_sent', COALESCE(total_sent, 0),
        'total_failed', COALESCE(total_failed, 0),
        'sent_today', COALESCE(total_today, 0),
        'success_rate',
        CASE
            WHEN (COALESCE(total_sent, 0) + COALESCE(total_failed, 0)) > 0
            THEN ROUND((COALESCE(total_sent, 0)::decimal / (COALESCE(total_sent, 0) + COALESCE(total_failed, 0))) * 100, 2)
            ELSE 0
        END
    );

    RETURN result_json;
END;
$$;

-- Create function to test SMS configuration
CREATE OR REPLACE FUNCTION public.test_sms_configuration()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    sms_enabled boolean;
    twilio_account_sid text;
    twilio_auth_token text;
    twilio_phone_number text;
    result_json json;
BEGIN
    -- Get notification settings
    SELECT
        ns.sms_enabled,
        ns.twilio_account_sid,
        ns.twilio_auth_token,
        ns.twilio_phone_number
    INTO
        sms_enabled,
        twilio_account_sid,
        twilio_auth_token,
        twilio_phone_number
    FROM notification_settings ns
    LIMIT 1;

    result_json := json_build_object(
        'sms_enabled', COALESCE(sms_enabled, false),
        'twilio_account_sid_configured', (twilio_account_sid IS NOT NULL AND length(twilio_account_sid) > 0),
        'twilio_auth_token_configured', (twilio_auth_token IS NOT NULL AND length(twilio_auth_token) > 0),
        'twilio_phone_number_configured', (twilio_phone_number IS NOT NULL AND length(twilio_phone_number) > 0),
        'configuration_complete', (
            COALESCE(sms_enabled, false) AND
            twilio_account_sid IS NOT NULL AND length(twilio_account_sid) > 0 AND
            twilio_auth_token IS NOT NULL AND length(twilio_auth_token) > 0 AND
            twilio_phone_number IS NOT NULL AND length(twilio_phone_number) > 0
        )
    );

    RETURN result_json;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.trigger_maturity_sms_alerts() TO authenticated;
GRANT EXECUTE ON FUNCTION public.send_maturity_alerts_sms() TO authenticated;
GRANT EXECUTE ON FUNCTION public.send_investment_sms_alert(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_sms_alert_stats(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.test_sms_configuration() TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION public.send_maturity_alerts_sms() IS 'Automated function to send SMS alerts for investments maturing in X days (configured in notification_settings). Runs daily via cron job.';
COMMENT ON FUNCTION public.trigger_maturity_sms_alerts() IS 'Manual trigger function for testing SMS maturity alerts.';
COMMENT ON FUNCTION public.send_investment_sms_alert(UUID, TEXT) IS 'Send SMS alert for a specific investment. Supports custom message with placeholders: {CLIENT_NAME}, {SCHEME_NAME}, {AMOUNT}, {MATURITY_AMOUNT}, {MATURITY_DATE}';
COMMENT ON FUNCTION public.get_sms_alert_stats(INTEGER) IS 'Get SMS alert statistics for the last N days (default 30).';
COMMENT ON FUNCTION public.test_sms_configuration() IS 'Test if SMS/Twilio configuration is properly set up.';

-- Create index for better performance on alerts table
CREATE INDEX IF NOT EXISTS idx_alerts_sms_channel_date ON public.alerts(channel, alert_date) WHERE channel = 'sms';
CREATE INDEX IF NOT EXISTS idx_alerts_investment_type_date ON public.alerts(investment_id, alert_type, alert_date);

-- Add sample usage examples in comments
/*
USAGE EXAMPLES:

1. Test SMS configuration:
   SELECT public.test_sms_configuration();

2. Manually trigger maturity SMS alerts:
   SELECT public.trigger_maturity_sms_alerts();

3. Send SMS for specific investment:
   SELECT public.send_investment_sms_alert('investment-uuid-here');

4. Send custom SMS for specific investment:
   SELECT public.send_investment_sms_alert(
     'investment-uuid-here',
     'Dear {CLIENT_NAME}, your {SCHEME_NAME} investment of {AMOUNT} is ready for action!'
   );

5. Get SMS statistics:
   SELECT public.get_sms_alert_stats(7); -- Last 7 days
   SELECT public.get_sms_alert_stats(); -- Last 30 days (default)

6. View recent SMS alerts:
   SELECT * FROM alerts WHERE channel = 'sms' ORDER BY created_at DESC LIMIT 10;

PHONE NUMBER HANDLING:
- Only sends to numbers with +91 country code
- Automatically formats phone numbers (adds +91 if missing)
- Sends to primary client mobile, primary client contact_person2, secondary client mobile, secondary client contact_person2
- Validates phone number format before sending

CRON JOB:
- Runs daily at 9 AM
- Can be modified using: SELECT cron.alter_job('send-maturity-alerts-sms-daily', schedule => '0 10 * * *');
- Can be disabled using: SELECT cron.unschedule('send-maturity-alerts-sms-daily');
*/
